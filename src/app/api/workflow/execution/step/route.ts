/**
 * API endpoint to update individual workflow steps with results and artifacts
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '../../../../../core/workflow/singleton';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { executionId, stepId, stepData } = body;

    console.log(`🔄 Updating step ${stepId} for execution: ${executionId}`);

    // Validate required fields
    if (!executionId || !stepId || !stepData) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: executionId, stepId, stepData'
      }, { status: 400 });
    }

    // Use the same workflow engine as the working executions
    const workflowEngine = getWorkflowEngine();

    // Get existing execution
    const execution = await workflowEngine.stateStore.getExecution(executionId);
    if (!execution) {
      return NextResponse.json({
        success: false,
        error: 'Execution not found'
      }, { status: 404 });
    }

    // Update stepResults with the new step data using proper StepResult interface
    const updatedStepResults = {
      ...execution.stepResults,
      [stepId]: {
        stepId,
        status: stepData.status,
        inputs: stepData.inputs || {},
        outputs: stepData.output || stepData.outputs || {},
        startedAt: stepData.startedAt,
        completedAt: stepData.completedAt,
        duration: stepData.duration,
        artifactId: stepData.output?.artifactId || stepData.outputs?.artifactId,
        metadata: {
          consultationResults: stepData.consultationResults
        }
      }
    };

    // Calculate progress based on completed steps
    const totalSteps = execution.steps?.length || 0;
    const completedSteps = Object.values(updatedStepResults).filter(
      (step: any) => step.status === 'completed'
    ).length;
    const progress = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

    // Update execution with new step results
    const updatedExecution = {
      ...execution,
      stepResults: updatedStepResults,
      progress,
      status: progress === 100 ? 'completed' : execution.status,
      completedAt: progress === 100 ? new Date().toISOString() : execution.completedAt,
      updatedAt: new Date().toISOString()
    };

    // Store updated execution
    await workflowEngine.stateStore.setExecution(updatedExecution);

    console.log(`✅ Updated step ${stepId} for execution: ${executionId} (${progress}% complete)`);

    return NextResponse.json({
      success: true,
      data: {
        executionId,
        stepId,
        status: stepData.status,
        progress,
        message: 'Step updated successfully'
      }
    });

  } catch (error) {
    console.error('❌ Failed to update workflow step:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update workflow step'
    }, { status: 500 });
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to update steps.'
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to update steps.'
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to update steps.'
  }, { status: 405 });
}
